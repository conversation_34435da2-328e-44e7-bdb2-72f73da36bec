# 🚀 VectorShift Pipeline Builder - Setup & Run Commands

## 📋 Prerequisites

Before running the application, ensure you have the following installed:

- **Node.js** (v16 or higher) - [Download here](https://nodejs.org/)
- **Python** (v3.8 or higher) - [Download here](https://python.org/)
- **Git** - [Download here](https://git-scm.com/)

## 🏃‍♂️ Quick Start Commands (Run from Root Directory)

### 1. Initial Setup (First Time Only)

```bash
# Clone the repository (if not already done)
git clone <repository-url>
cd frontend_technical_assessment

# Setup Backend Environment
cd backend
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install backend dependencies
pip install -r requirements.txt

# Return to root directory
cd ..

# Setup Frontend Environment
cd frontend
npm install

# Return to root directory
cd ..
```

### 2. Development Mode (Daily Usage)

#### Option A: Run Both Services Simultaneously (Recommended)

**Windows (PowerShell):**
```powershell
# Terminal 1 - Backend
cd backend
venv\Scripts\activate
uvicorn main:app --reload --port 8000

# Terminal 2 - Frontend (open new terminal)
cd frontend
npm start
```

**macOS/Linux (Bash):**
```bash
# Terminal 1 - Backend
cd backend
source venv/bin/activate
uvicorn main:app --reload --port 8000

# Terminal 2 - Frontend (open new terminal)
cd frontend
npm start
```

#### Option B: Run Services Separately

**Backend Only:**
```bash
cd backend
# Windows: venv\Scripts\activate
# macOS/Linux: source venv/bin/activate
uvicorn main:app --reload --port 8000
```

**Frontend Only:**
```bash
cd frontend
npm start
```

### 3. Production Build

```bash
# Build Frontend for Production
cd frontend
npm run build

# Run Backend in Production Mode
cd ../backend
# Windows: venv\Scripts\activate
# macOS/Linux: source venv/bin/activate
uvicorn main:app --host 0.0.0.0 --port 8000
```

### 4. Testing Commands

```bash
# Frontend Tests
cd frontend
npm test

# Backend Tests (after installing test dependencies)
cd backend
# Windows: venv\Scripts\activate
# macOS/Linux: source venv/bin/activate
python -m pytest

# Run specific test file
python -m pytest test_main.py -v
```

### 5. Dependency Management

```bash
# Update Frontend Dependencies
cd frontend
npm update
npm audit fix

# Update Backend Dependencies
cd backend
# Windows: venv\Scripts\activate
# macOS/Linux: source venv/bin/activate
pip install --upgrade -r requirements.txt

# Add new frontend dependency
cd frontend
npm install <package-name>

# Add new backend dependency
cd backend
pip install <package-name>
pip freeze > requirements.txt
```

### 6. Environment Management

```bash
# Create new virtual environment (if needed)
cd backend
python -m venv venv

# Deactivate virtual environment
deactivate

# Remove virtual environment (if needed)
# Windows: rmdir /s venv
# macOS/Linux: rm -rf venv
```

## 🌐 Application URLs

After running the development commands:

- **Frontend Application**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **API Redoc**: http://localhost:8000/redoc

## 🔧 Troubleshooting

### Common Issues:

1. **Port Already in Use:**
   ```bash
   # Kill process on port 3000 (frontend)
   # Windows: netstat -ano | findstr :3000
   # macOS/Linux: lsof -ti:3000 | xargs kill -9
   
   # Kill process on port 8000 (backend)
   # Windows: netstat -ano | findstr :8000
   # macOS/Linux: lsof -ti:8000 | xargs kill -9
   ```

2. **Python Virtual Environment Issues:**
   ```bash
   # Recreate virtual environment
   cd backend
   rm -rf venv  # or rmdir /s venv on Windows
   python -m venv venv
   # Activate and reinstall dependencies
   ```

3. **Node Modules Issues:**
   ```bash
   # Clear npm cache and reinstall
   cd frontend
   rm -rf node_modules package-lock.json
   npm cache clean --force
   npm install
   ```

4. **CORS Issues:**
   - Ensure backend is running on port 8000
   - Check that frontend is accessing http://localhost:8000
   - Verify CORS settings in backend/main.py

## 📊 Development Workflow

1. **Start Development Session:**
   ```bash
   # Open 2 terminals and run:
   # Terminal 1: Backend
   cd backend && venv\Scripts\activate && uvicorn main:app --reload --port 8000
   
   # Terminal 2: Frontend
   cd frontend && npm start
   ```

2. **Make Changes:**
   - Frontend changes auto-reload at http://localhost:3000
   - Backend changes auto-reload with `--reload` flag

3. **Test Changes:**
   ```bash
   # Test frontend
   cd frontend && npm test
   
   # Test backend
   cd backend && python -m pytest
   ```

4. **Build for Production:**
   ```bash
   cd frontend && npm run build
   ```

## 🎯 Quick Commands Reference

| Task | Command |
|------|---------|
| Start Backend | `cd backend && uvicorn main:app --reload --port 8000` |
| Start Frontend | `cd frontend && npm start` |
| Install Backend Deps | `cd backend && pip install -r requirements.txt` |
| Install Frontend Deps | `cd frontend && npm install` |
| Run Tests | `cd frontend && npm test` or `cd backend && python -m pytest` |
| Build Production | `cd frontend && npm run build` |
| View API Docs | Open http://localhost:8000/docs |

---

**💡 Tip:** Keep both terminals open during development for the best experience!
