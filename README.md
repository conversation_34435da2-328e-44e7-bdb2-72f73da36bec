# 🚀 VectorShift Pipeline Builder - Technical Assessment

A sophisticated pipeline builder application built with React (Frontend) and FastAPI (Backend) that demonstrates advanced node-based visual programming capabilities.

## 🏗️ Architecture Overview

- **Frontend**: React 18 with ReactFlow for node-based UI
- **Backend**: FastAPI with advanced DAG validation
- **State Management**: Zustand for efficient React state
- **Styling**: Custom CSS with Studio Feixen Sans typography
- **Design**: Professional glassmorphic UI with responsive layout

## ✨ Key Features

### 🧩 **Node Abstraction System**
- **9 Node Types**: Input, Output, LLM, Text, Math, Filter, Timer, Switch, Aggregator
- **BaseNode Component**: Shared abstraction for consistent styling and behavior
- **Dynamic Handles**: Auto-generated connection points based on node content

### 💬 **Advanced Text Node**
- **Variable Parsing**: Detects `{{variableName}}` syntax in real-time
- **Dynamic Resizing**: Auto-adjusts width/height based on content
- **Handle Generation**: Creates input handles for each detected variable
- **Visual Feedback**: Shows parsed variables with professional styling

### 🎨 **Professional Design System**
- **Studio Feixen Sans**: Custom font implementation with multiple weights
- **Responsive Layout**: Mobile, tablet, and desktop optimized
- **Glassmorphic Effects**: Modern blur effects and gradients
- **Smooth Animations**: Professional hover states and transitions

### 🔄 **Backend Integration**
- **DAG Validation**: Sophisticated cycle detection using DFS algorithm
- **Pipeline Analysis**: Real-time node/edge counting and validation
- **Error Handling**: Comprehensive error management with user-friendly feedback
- **CORS Support**: Configured for seamless frontend-backend communication

## 🚀 Quick Start

### Prerequisites
- Node.js (v16+)
- Python (v3.8+)
- npm or yarn

### 1. Clone Repository
```bash
git clone <repository-url>
cd frontend_technical_assessment
```

### 2. Setup Backend
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn main:app --reload --port 8000
```

### 3. Setup Frontend
```bash
cd frontend
npm install
npm start
```

### 4. Access Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs

## 📁 Project Structure

```
frontend_technical_assessment/
├── frontend/                 # React application
│   ├── src/
│   │   ├── components/      # Reusable UI components
│   │   ├── nodes/           # Node type implementations
│   │   ├── styles/          # Design system and themes
│   │   └── ...
│   └── package.json
├── backend/                 # FastAPI application
│   ├── main.py             # API endpoints and DAG validation
│   └── requirements.txt
└── README.md
```

## 🎯 Assessment Requirements Fulfilled

### ✅ Part 1: Node Abstraction
- [x] BaseNode component for shared functionality
- [x] 9 node types using common abstraction
- [x] Modular and maintainable architecture

### ✅ Part 2: Styling
- [x] Unified design system with Studio Feixen Sans
- [x] Responsive layout for all devices
- [x] Professional glassmorphic UI
- [x] Optimized node dimensions

### ✅ Part 3: Text Node Logic
- [x] Auto-resizing based on content
- [x] Variable parsing with `{{variable}}` syntax
- [x] Dynamic handle generation
- [x] Real-time visual feedback

### ✅ Part 4: Backend Integration
- [x] FastAPI endpoint `/pipelines/parse`
- [x] DAG validation with cycle detection
- [x] Professional modal display of results
- [x] Comprehensive error handling

## 🛠️ Development

### Running Tests
```bash
# Frontend tests
cd frontend
npm test

# Backend tests
cd backend
python -m pytest
```

### Building for Production
```bash
# Frontend build
cd frontend
npm run build

# Backend deployment
cd backend
uvicorn main:app --host 0.0.0.0 --port 8000
```

## 🎨 Design Features

- **Color Palette**: Purple/blue gradients (#8b5cf6, #6366f1)
- **Typography**: Studio Feixen Sans with fallbacks
- **Animations**: Smooth transitions and hover effects
- **Responsive**: Breakpoints at 360px, 480px, 768px, 1200px, 1600px
- **Accessibility**: Keyboard navigation and screen reader support

## 🔧 Technical Highlights

- **State Management**: Zustand for efficient React state
- **Performance**: Optimized rendering with shallow comparisons
- **Error Boundaries**: Graceful error handling throughout
- **Code Quality**: Clean architecture with comprehensive comments
- **Modern Patterns**: Hooks, functional components, and TypeScript-ready

## 📱 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
